import { redirect } from "next/navigation"
import { getCurrentSession, type SessionValidationR<PERSON>ult } from "./session"

export type ValidatedSession = Exclude<
  SessionValidationResult,
  { session: null; user: null }
>

type Handler<TArgs extends unknown[], TReturnType> = (
  session: ValidatedSession,
  ...args: TArgs
) => Promise<TReturnType>

export function withSessionValidated<TArgs extends unknown[], TReturnType>(
  handler: <PERSON><PERSON><TArgs, TReturnType>
) {
  return async (...args: TArgs): Promise<TReturnType> => {
    const session = await getCurrentSession()

    if (!session.user || !session.session) {
      return redirect("/")
    }

    return handler(session, ...args)
  }
}

export function withSessionValidatedPage<TProps extends object>(
  Component: React.ComponentType<TProps & { session: ValidatedSession }>
) {
  const Wrapper = async (props: TProps) => {
    const session = await getCurrentSession()

    if (!session.user || !session.session) {
      return redirect("/")
    }

    return <Component session={session} {...props} />
  }

  Wrapper.displayName = `withSessionValidatedPage(${
    Component.displayName || Component.name || "Anonymous"
  })`

  return Wrapper
}

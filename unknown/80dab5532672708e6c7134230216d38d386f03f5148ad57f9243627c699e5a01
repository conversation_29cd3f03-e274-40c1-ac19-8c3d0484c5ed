[package]
name = "nextjs_runtime_demo"
version = "0.1.0"
edition = "2021"
publish = false

[dependencies]
tokio = { version = "1", features = ["macros"] }
tracing = { version = "0.1", features = ["log"] }
tracing-subscriber = { version = "0.3", default-features = false, features = [
  "fmt",
] }
serde = { version = "1.0.188", features = ["derive"] }
serde_json = { version = "1.0.106", features = ["raw_value"] }
serde_derive = "1.0.188"
rand = "0.8.5"
oorandom = "11.1.3"
vercel_runtime = "1.1.3"
# vercel_runtime = { version = "1.1.0", path = "../../crates/vercel_runtime" }

[[bin]]
name = "index"
path = "api/check-solution/index.rs"

# Sokoverse

Sokoverse is a modern take on the classic Sokoban puzzle game, featuring **procedural level generation** and multiple game modes. The game is built using **Next.js**, **TypeScript**, and **Tailwind CSS**.

## ✨ Features

- **🎲 Procedural Level Generation** – Every level is uniquely generated, providing infinite puzzles
- **🎯 Multiple Game Modes**
  - **Endless Mode** – Infinite levels with consistent difficulty
  - **Expert Mode** – Progressive difficulty increase with each level
- **🎨 Retro-Pixel Design** – Classic aesthetics with modern responsiveness
- **🌙 Theme Support** – Multiple color themes (green, blue, purple, monochrome)

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- PNPM package manager

### Installation

1. Clone the repository
   ```sh
   git clone https://github.com/your-username/sokoverse.git
   cd sokoverse
   ```

2. Install dependencies
   ```sh
   pnpm install
   ```

3. Start the development server
   ```sh
   pnpm dev
   ```

4. Open [http://localhost:3000](http://localhost:3000) in your browser

## 🛠️ Built With

- **Framework**: [Next.js 15](https://nextjs.org/) with React 19
- **Styling**: [Tailwind CSS](https://tailwindcss.com/) with custom pixel-art components
- **UI Components**: [Radix UI](https://www.radix-ui.com/) primitives
- **State Management**: [TanStack Query](https://tanstack.com/query)
- **Type Safety**: TypeScript
- **Package Manager**: PNPM

## 🗺️ Planned

- **Daily Challenge Mode** – Compete on global leaderboards
- **Two-Player Mode** – Real-time multiplayer puzzles
- **Sound Effects** – Immersive audio feedback
- **Level Creator** – Design and share custom levels

## 🎮 Game Controls

- **Arrow Keys** / **WASD** – Move the player
- **R** – Reset level
- **Z** – Undo move

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the project
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details


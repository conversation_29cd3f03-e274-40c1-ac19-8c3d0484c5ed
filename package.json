{"name": "sokoverse", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@google/genai": "^1.0.1", "@hookform/resolvers": "^3.9.1", "@neondatabase/serverless": "^1.0.0", "@oslojs/crypto": "^1.0.1", "@oslojs/encoding": "^1.1.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@react-pdf/renderer": "^4.3.0", "@tanstack/react-query": "^5.71.3", "arctic": "^3.6.0", "autoprefixer": "^10.4.20", "babel-plugin-react-compiler": "19.1.0-rc.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "drizzle-orm": "^0.41.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.4.0-canary.37", "next-plausible": "^3.12.4", "next-safe-action": "^7.10.8", "next-themes": "latest", "nextjs-toploader": "^3.8.16", "plausible-tracker": "^0.3.9", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "seedrandom": "latest", "server-only": "^0.0.1", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.2"}, "devDependencies": {"@types/next": "^9.0.0", "@types/node": "^22.13.17", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.30.6", "eslint": "^9", "eslint-config-next": "15.2.4", "postcss": "^8", "tailwindcss": "^3.4.17", "tsx": "^4.19.3", "typescript": "^5"}}
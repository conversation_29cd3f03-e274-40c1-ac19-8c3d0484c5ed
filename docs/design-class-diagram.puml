@startuml Sokoverse Design Class Diagram

!theme plain
skinparam class {
    BackgroundColor #F8F9FA
    BorderColor #495057
    FontSize 9
    AttributeFontSize 8
}
skinparam interface {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontSize 9
}
skinparam package {
    BackgroundColor #E9ECEF
    BorderColor #6C757D
    FontSize 10
    FontStyle bold
}

title Sokoverse - Design Class Diagram

package "Client-Side Game Logic" {
    interface GameState {
        +grid: string[][]
        +playerPos: Position
        +prevPlayerPos: Position
        +steps: number
        +startTime: number
        +elapsedTime: number
        +isCompleted: boolean
        +movementDirection: Direction
        +isMoving: boolean
        +moves: LURDMove[]
    }
    
    class GameLogic {
        +initializeGameState(level: string[]): GameState
        +movePlayer(state: GameState, direction: Direction): GameState
        +resetLevel(level: string[]): GameState
        +checkLevelCompleted(grid: string[][]): boolean
        +getGameStats(state: GameState): GameStats
        +formatTime(ms: number): string
    }
    
    class Position {
        +x: number
        +y: number
    }
    
    enum Direction {
        UP, DOWN, LEFT, RIGHT
    }
    
    enum LURDMove {
        L, U, R, D
    }
}

package "React Components" {
    abstract class GameComponent {
        #gameState: GameState
        #setGameState: Function
        #onReset: Function
        +render(): JSX.Element
    }
    
    class EndlessGame extends GameComponent {
        -generateLevelAction: SafeAction
        -submitLevelAction: SafeAction
        -settings: EndlessSettings
        +handleLevelComplete(): void
        +generateNewLevel(): void
    }
    
    class SpikeVaultGame extends GameComponent {
        -vaultId: string
        -levelNumber: number
        +completeSpikeVaultLevel(): void
    }
    
    class BoxobanGame extends GameComponent {
        -levelId: string
        -category: string
        +completeBoxobanLevel(): void
        +unassignLevel(): void
    }
    
    class OverclockGame extends GameComponent {
        -currentLevel: number
        -hasAccess: boolean
        +verifyPremiumAccess(): void
    }
    
    class SokobanCanvasGameBoard {
        -canvasRef: HTMLCanvasElement
        -animationFrame: AnimationFrame
        -canvasDimensions: CanvasDimensions
        +render(): void
        +handleResize(): void
    }
    
    class LevelCompletionDialog {
        -isOpen: boolean
        -stats: GameStats
        -mode: string
        +onNextLevel(): void
        +onReplayLevel(): void
    }
}

package "Custom Hooks" {
    class useKeyboardControls {
        +handleKeyDown(event: KeyboardEvent): void
        +handleKeyUp(event: KeyboardEvent): void
    }
    
    class useGameTimer {
        -timerRef: NodeJS.Timeout
        +updateGameTime(): void
    }
    
    class useGameCompletion {
        +checkCompletion(): void
        +onLevelComplete(): void
    }
    
    class useAuth {
        +user: User
        +isAuthenticated: boolean
    }
}

package "Context Providers" {
    class AuthProvider {
        -initialState: AuthContextType
        +value: AuthContextType
    }
    
    class ThemeProvider {
        -themes: string[]
        -defaultTheme: string
        +currentTheme: string
    }
    
    class QueryClientProvider {
        -queryClient: QueryClient
        +client: QueryClient
    }
}

package "Server Actions" {
    abstract class SafeActionClient {
        #authActionClient: ActionClient
        #actionClient: ActionClient
        +metadata: ActionMetadata
        +schema: ZodSchema
        +action: ActionFunction
    }
    
    class EndlessActions extends SafeActionClient {
        +generateEndlessLevel(): Promise<LevelData>
        +submitLevel(stats, moves, hash): Promise<void>
        +updateLevel(stats, moves, hash): Promise<void>
        +getEndlessRecords(params): Promise<EndlessLevel[]>
    }
    
    class SpikeVaultActions extends SafeActionClient {
        +createSpikeVault(data): Promise<SpikeVault>
        +editSpikeVault(id, data): Promise<void>
        +deleteSpikeVault(id): Promise<void>
        +completeSpikeVaultLevel(params): Promise<void>
    }
    
    class BoxobanActions extends SafeActionClient {
        +getNextBoxobanLevel(): Promise<BoxobanLevel>
        +completeBoxobanLevel(params): Promise<void>
        +setBoxobanMode(mode): Promise<void>
    }
    
    class OverclockActions extends SafeActionClient {
        +generateOverclockLevel(): Promise<LevelData>
        +submitLevel(params): Promise<void>
        +checkOverclockAccess(userId): Promise<boolean>
    }
    
    class AuthActions extends SafeActionClient {
        +signOut(): Promise<void>
        +anonymousSignIn(): Promise<User>
    }
    
    class ReviewActions extends SafeActionClient {
        +createReview(data): Promise<UserReview>
        +updateReview(id, data): Promise<void>
    }
}

package "Database Layer" {
    class DatabaseSchema {
        +userTable: PgTable
        +sessionTable: PgTable
        +endlessUserData: PgTable
        +endlessLevels: PgTable
        +spikeVaults: PgTable
        +spikeVaultLevels: PgTable
        +boxobanLevels: PgTable
        +boxobanUserData: PgTable
        +overclockUserData: PgTable
        +overclockLevels: PgTable
        +userReviews: PgTable
    }
    
    class DrizzleORM {
        +db: DrizzleDatabase
        +select(): SelectBuilder
        +insert(): InsertBuilder
        +update(): UpdateBuilder
        +delete(): DeleteBuilder
    }
}

package "Authentication Services" {
    class SessionManager {
        +generateSessionToken(): string
        +createSession(token, userId): Promise<Session>
        +getCurrentSession(): Promise<ValidatedSession>
        +invalidateSession(sessionId): Promise<void>
        +deleteSessionTokenCookie(): void
    }
    
    class AnonymousAuth {
        +createAnonymousUser(): Promise<{user, session}>
    }
    
    class GoogleOAuth {
        +google: Google
        +handleCallback(): Promise<User>
    }
    
    class HMACService {
        +signPayload(payload: string): string
        +hmacSign(payload: string): Promise<string>
    }
}

package "Level Generation Services" {
    class LevelGenerator {
        +generateSokobanLevelServerSide(params): Promise<SokobanLevelResult>
        +generateSokobanLevelClientSide(params): Promise<SokobanLevelResult>
    }
    
    class AutoSokoban {
        +generateSpikeVaultLevel(seed, difficulty): Promise<LevelData>
    }
    
    class SolutionChecker {
        +checkSolution(level, solution): Promise<boolean>
    }
    
    class Grid {
        -width: number
        -height: number
        -boxes: number
        -seed: number
        +applyTemplates(): boolean
        +isGoodCandidate(): boolean
        +redeployGoals(): boolean
        +generateFarthestBoxes(): boolean
    }
}

package "Validation & Security" {
    class ValidationSchemas {
        +createSpikeVaultSchema: ZodSchema
        +createReviewSchema: ZodSchema
        +endlessRecordsParamsSchema: ZodSchema
        +submitLevelParamsSchema: ZodSchema
    }
    
    class ActionError extends Error {
        +message: string
    }
    
    class SecurityService {
        +verifyHash(payload, hash): boolean
        +validateTime(moves, time): boolean
        +validateSolution(level, moves): Promise<boolean>
    }
}

package "UI Components" {
    class Button {
        +variant: ButtonVariant
        +size: ButtonSize
        +asChild: boolean
        +onClick: Function
    }
    
    class Dialog {
        +open: boolean
        +onOpenChange: Function
        +children: ReactNode
    }
    
    class Form {
        +form: UseFormReturn
        +onSubmit: Function
        +children: ReactNode
    }
    
    class Toast {
        +title: string
        +description: string
        +variant: ToastVariant
    }
}

' Relationships
GameComponent --> GameState : uses
GameComponent --> GameLogic : uses
EndlessGame --> EndlessActions : calls
SpikeVaultGame --> SpikeVaultActions : calls
BoxobanGame --> BoxobanActions : calls
OverclockGame --> OverclockActions : calls

SafeActionClient --> DatabaseSchema : accesses
SafeActionClient --> ValidationSchemas : validates
SafeActionClient --> SecurityService : secures

SessionManager --> DatabaseSchema : persists
LevelGenerator --> Grid : uses
SolutionChecker --> SecurityService : validates

useAuth --> AuthProvider : consumes
GameComponent --> useKeyboardControls : uses
GameComponent --> useGameTimer : uses
GameComponent --> useGameCompletion : uses

@enduml

@startuml Sokoverse Use Case Diagram

!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontSize 12
}
skinparam usecase {
    BackgroundColor #F5F5F5
    BorderColor #424242
    FontSize 10
}
skinparam package {
    BackgroundColor #FAFAFA
    BorderColor #757575
    FontSize 11
    FontStyle bold
}

title Sokoverse - High Level Use Case Diagram

' Actors
actor "Guest User" as Guest
actor "Authenticated Player" as Player
actor "Administrator" as Admin

' Authentication System
package "Authentication System" {
    usecase "Sign In Anonymously" as UC_Auth1
    usecase "Sign In with Google" as UC_Auth2
    usecase "Sign Out" as UC_Auth3
}

' Game Modes
package "Game Modes" {
    package "Endless Mode" {
        usecase "Configure Difficulty Settings" as UC_Endless1
        usecase "Play Procedural Levels" as UC_Endless2
        usecase "View Progress Records" as UC_Endless3
        usecase "Continue Game Session" as UC_Endless4
    }
    
    package "Spike Vaults" {
        usecase "Create Custom Vault" as UC_Vault1
        usecase "Edit Vault Settings" as UC_Vault2
        usecase "Delete Vault" as UC_Vault3
        usecase "Play Vault Levels" as UC_Vault4
        usecase "View Vault Progress" as UC_Vault5
        usecase "Share Vault" as UC_Vault6
    }
    
    package "Boxoban Challenge" {
        usecase "Select Difficulty Mode" as UC_Boxoban1
        usecase "Play Global Challenges" as UC_Boxoban2
        usecase "View Global Progress" as UC_Boxoban3
        usecase "Complete Synchronized Levels" as UC_Boxoban4
    }
    
    package "Overclock Mode" {
        usecase "Verify Premium Access" as UC_Overclock1
        usecase "Play High-Difficulty Levels" as UC_Overclock2
        usecase "View Overclock Records" as UC_Overclock3
        usecase "Continue Premium Session" as UC_Overclock4
    }
}

' Game Mechanics
package "Game Mechanics" {
    usecase "Move Player Character" as UC_Game1
    usecase "Push Boxes to Goals" as UC_Game2
    usecase "Reset Current Level" as UC_Game3
    usecase "Track Game Statistics" as UC_Game4
    usecase "Submit Level Completion" as UC_Game5
    usecase "View Game Controls" as UC_Game6
}

' Content Management
package "Content Management" {
    usecase "Generate Procedural Levels" as UC_Content1
    usecase "Validate Solutions" as UC_Content2
    usecase "Save Game Progress" as UC_Content3
    usecase "Load Game State" as UC_Content4
}

' User Reviews System
package "User Reviews" {
    usecase "Submit Game Review" as UC_Review1
    usecase "Rate Game Experience" as UC_Review2
    usecase "View Public Reviews" as UC_Review3
}

' Admin Features
package "Admin Panel" {
    usecase "View User Analytics" as UC_Admin1
    usecase "Monitor Game Statistics" as UC_Admin2
    usecase "Manage User Reviews" as UC_Admin3
    usecase "Approve/Reject Reviews" as UC_Admin4
    usecase "View System Dashboard" as UC_Admin5
    usecase "Track User Engagement" as UC_Admin6
}

' Guest User Relationships
Guest --> UC_Auth1
Guest --> UC_Auth2
Guest --> UC_Review3

' Authenticated Player Relationships
Player --> UC_Auth3
Player --> UC_Endless1
Player --> UC_Endless2
Player --> UC_Endless3
Player --> UC_Endless4
Player --> UC_Vault1
Player --> UC_Vault2
Player --> UC_Vault3
Player --> UC_Vault4
Player --> UC_Vault5
Player --> UC_Vault6
Player --> UC_Boxoban1
Player --> UC_Boxoban2
Player --> UC_Boxoban3
Player --> UC_Boxoban4
Player --> UC_Overclock1
Player --> UC_Overclock2
Player --> UC_Overclock3
Player --> UC_Overclock4
Player --> UC_Game1
Player --> UC_Game2
Player --> UC_Game3
Player --> UC_Game4
Player --> UC_Game5
Player --> UC_Game6
Player --> UC_Review1
Player --> UC_Review2
Player --> UC_Review3

' Administrator Relationships
Admin --> UC_Admin1
Admin --> UC_Admin2
Admin --> UC_Admin3
Admin --> UC_Admin4
Admin --> UC_Admin5
Admin --> UC_Admin6
Admin --> UC_Review3

' System Dependencies (includes)
UC_Endless2 ..> UC_Content1 : <<include>>
UC_Vault4 ..> UC_Content1 : <<include>>
UC_Boxoban2 ..> UC_Content4 : <<include>>
UC_Overclock2 ..> UC_Content1 : <<include>>

UC_Game5 ..> UC_Content2 : <<include>>
UC_Game5 ..> UC_Content3 : <<include>>
UC_Endless4 ..> UC_Content4 : <<include>>
UC_Vault5 ..> UC_Content4 : <<include>>
UC_Overclock4 ..> UC_Content4 : <<include>>

UC_Review1 ..> UC_Admin3 : <<include>>
UC_Review2 ..> UC_Admin3 : <<include>>

@enduml

@startuml Sokoverse Domain Model

!theme plain
skinparam class {
    BackgroundColor #F8F9FA
    BorderColor #495057
    FontSize 10
    AttributeFontSize 9
}
skinparam package {
    BackgroundColor #E9ECEF
    BorderColor #6C757D
    FontSize 11
    FontStyle bold
}

title Sokoverse - Domain Model Diagram

package "User Management" {
    class User {
        +id: Integer {PK}
        +googleId: String {unique}
        +name: String
        +pictureURL: String
        +isAnonymous: Boolean = true
        +createdAt: Timestamp
    }
    
    class Session {
        +id: String {PK}
        +userId: Integer {FK}
        +expiresAt: Timestamp
    }
    
    class UserReview {
        +id: Integer {PK}
        +userId: Integer {FK}
        +reviewText: String
        +starRating: Real
        +approved: Boolean = false
        +createdAt: Timestamp
        +updatedAt: Timestamp
    }
}

package "Endless Mode" {
    class EndlessUserData {
        +id: Integer {PK}
        +userId: Integer {FK, unique}
        +settings: JSON<EndlessSettings>
        +levelCount: Integer = 0
        +customWidth: Integer
        +customHeight: Integer
        +customBoxes: Integer
        +customMinWalls: Integer
        +updatedAt: Timestamp
    }
    
    class EndlessLevel {
        +id: UUID {PK}
        +userId: Integer {FK}
        +levelData: String[]
        +levelNumber: Integer
        +setting: JSON<EndlessSettings>
        +isCompleted: Boolean = false
        +steps: Integer
        +timeMs: Integer
        +createdAt: Timestamp
        +completedAt: Timestamp
    }
}

package "Spike Vaults" {
    class SpikeVault {
        +id: UUID {PK}
        +userId: Integer {FK}
        +name: String
        +slug: String
        +seed: String
        +description: String
        +depthGoal: Integer = 20
        +currentDepth: Integer = 0
        +status: String = "in_progress"
        +deleted: Boolean = false
        +createdAt: Timestamp
    }
    
    class SpikeVaultLevel {
        +id: UUID {PK}
        +userId: Integer {FK}
        +spikeVaultId: UUID {FK}
        +levelNumber: Integer
        +levelData: String[]
        +completed: Boolean = false
        +steps: Integer
        +timeMs: Integer
        +createdAt: Timestamp
        +completedAt: Timestamp
    }
}

package "Boxoban Challenge" {
    class BoxobanLevel {
        +levelId: String {PK}
        +category: String
        +fileNumber: Integer
        +levelNumber: Integer
        +assignedTo: Integer {FK}
        +status: String = "available"
        +updatedAt: Timestamp
    }
    
    class BoxobanUserData {
        +userId: Integer {PK, FK}
        +currentLevelId: String {FK}
        +mode: String
        +mediumSolved: Integer = 0
        +hardSolved: Integer = 0
        +unfilteredSolved: Integer = 0
        +totalSolved: Integer = 0
        +updatedAt: Timestamp
    }
    
    class BoxobanGlobalProgress {
        +category: String {PK}
        +totalLevels: Integer
        +solvedLevels: Integer = 0
        +discardedLevels: Integer = 0
        +updatedAt: Timestamp
    }
}

package "Overclock Mode" {
    class OverclockUserData {
        +id: Integer {PK}
        +userId: Integer {FK, unique}
        +currentLevel: Integer = 0
        +updatedAt: Timestamp
    }
    
    class OverclockLevel {
        +id: UUID {PK}
        +userId: Integer {FK}
        +levelNumber: Integer
        +levelData: String[]
        +completed: Boolean = false
        +steps: Integer
        +timeMs: Integer
        +createdAt: Timestamp
        +completedAt: Timestamp
    }
}

' Relationships
User ||--o{ Session : "has sessions"
User ||--o{ UserReview : "writes reviews"
User ||--o| EndlessUserData : "has endless data"
User ||--o{ EndlessLevel : "plays endless levels"
User ||--o{ SpikeVault : "creates vaults"
User ||--o{ SpikeVaultLevel : "plays vault levels"
User ||--o{ BoxobanLevel : "assigned levels"
User ||--o| BoxobanUserData : "has boxoban data"
User ||--o| OverclockUserData : "has overclock data"
User ||--o{ OverclockLevel : "plays overclock levels"

SpikeVault ||--o{ SpikeVaultLevel : "contains levels"
BoxobanLevel ||--o| BoxobanUserData : "current level"

' Constraints and Notes
note right of User : "Users can be anonymous\nor Google authenticated"

note right of SpikeVault : "Unique constraint:\n(userId, slug)"

note right of UserReview : "Unique constraint:\n(userId)\nOne review per user"

note right of BoxobanLevel : "Level ID format:\n{category}-{fileIndex}-{levelIndex}"

note right of BoxobanGlobalProgress : "Tracks global progress\nfor each difficulty category"

note right of OverclockUserData : "Premium mode requiring\npayment verification"

@enduml

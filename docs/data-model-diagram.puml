@startuml Sokoverse Data Model Diagram

!theme plain
skinparam entity {
    BackgroundColor #F8F9FA
    BorderColor #495057
    FontSize 10
    AttributeFontSize 9
}
skinparam note {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontSize 9
}

title Sokoverse - Data Model Diagram

entity "userTable" as User {
    * **id** : INTEGER <<PK, IDENTITY>>
    --
    googleId : TEXT <<UNIQUE>>
    name : TEXT
    pictureURL : TEXT
    isAnonymous : BOOLEAN = TRUE
    createdAt : TIMESTAMP = NOW()
}

entity "session" as Session {
    * **id** : TEXT <<PK>>
    --
    * userId : INTEGER <<FK>>
    * expiresAt : TIMESTAMP
}

entity "endless_user_data" as EndlessUserData {
    * **id** : INTEGER <<PK, IDENTITY>>
    --
    * userId : INTEGER <<FK, UNIQUE>>
    settings : JSONB<EndlessSettings>
    levelCount : INTEGER = 0
    customWidth : INTEGER
    customHeight : INTEGER
    customBoxes : INTEGER
    customMinWalls : INTEGER
    updatedAt : TIMESTAMP = NOW()
}

entity "endless_levels" as EndlessLevels {
    * **id** : UUID <<PK>>
    --
    userId : INTEGER <<FK>>
    * levelData : TEXT[]
    * levelNumber : INTEGER
    setting : JSONB<EndlessSettings>
    isCompleted : BOOLEAN = FALSE
    steps : INTEGER
    timeMs : INTEGER
    createdAt : TIMESTAMP = NOW()
    completedAt : TIMESTAMP = NOW()
}

entity "spike_vaults" as SpikeVaults {
    * **id** : UUID <<PK>>
    --
    userId : INTEGER <<FK>>
    * name : TEXT
    * slug : TEXT
    * seed : TEXT
    description : TEXT
    depthGoal : INTEGER = 20
    currentDepth : INTEGER = 0
    status : TEXT = "in_progress"
    deleted : BOOLEAN = FALSE
    createdAt : TIMESTAMP = NOW()
    --
    UNIQUE(userId, slug)
}

entity "spike_vault_levels" as SpikeVaultLevels {
    * **id** : UUID <<PK>>
    --
    userId : INTEGER <<FK>>
    spikeVaultId : UUID <<FK>>
    * levelNumber : INTEGER
    * levelData : TEXT[]
    completed : BOOLEAN = FALSE
    steps : INTEGER
    timeMs : INTEGER
    createdAt : TIMESTAMP = NOW()
    completedAt : TIMESTAMP
}

entity "boxoban_levels" as BoxobanLevels {
    * **levelId** : TEXT <<PK>>
    --
    * category : TEXT
    * fileNumber : INTEGER
    * levelNumber : INTEGER
    assignedTo : INTEGER <<FK>>
    status : TEXT = "available"
    updatedAt : TIMESTAMP = NOW()
}

entity "boxoban_user_data" as BoxobanUserData {
    * **userId** : INTEGER <<PK, FK>>
    --
    currentLevelId : TEXT <<FK>>
    * mode : TEXT
    mediumSolved : INTEGER = 0
    hardSolved : INTEGER = 0
    unfilteredSolved : INTEGER = 0
    totalSolved : INTEGER = 0
    updatedAt : TIMESTAMP = NOW()
}

entity "boxoban_global_progress" as BoxobanGlobalProgress {
    * **category** : TEXT <<PK>>
    --
    * totalLevels : INTEGER
    solvedLevels : INTEGER = 0
    discardedLevels : INTEGER = 0
    updatedAt : TIMESTAMP = NOW()
}

entity "overclock_user_data" as OverclockUserData {
    * **id** : INTEGER <<PK, IDENTITY>>
    --
    * userId : INTEGER <<FK, UNIQUE>>
    currentLevel : INTEGER = 0
    updatedAt : TIMESTAMP = NOW()
}

entity "overclock_levels" as OverclockLevels {
    * **id** : UUID <<PK>>
    --
    userId : INTEGER <<FK>>
    * levelNumber : INTEGER
    * levelData : TEXT[]
    completed : BOOLEAN = FALSE
    steps : INTEGER
    timeMs : INTEGER
    createdAt : TIMESTAMP = NOW()
    completedAt : TIMESTAMP
}

entity "user_reviews" as UserReviews {
    * **id** : INTEGER <<PK, IDENTITY>>
    --
    * userId : INTEGER <<FK>>
    * reviewText : TEXT
    * starRating : REAL
    approved : BOOLEAN = FALSE
    createdAt : TIMESTAMP = NOW()
    updatedAt : TIMESTAMP = NOW()
    --
    UNIQUE(userId)
}

' Relationships
User ||--o{ Session : "userId"
User ||--o| EndlessUserData : "userId"
User ||--o{ EndlessLevels : "userId"
User ||--o{ SpikeVaults : "userId"
User ||--o{ SpikeVaultLevels : "userId"
User ||--o{ BoxobanLevels : "assignedTo"
User ||--o| BoxobanUserData : "userId"
User ||--o| OverclockUserData : "userId"
User ||--o{ OverclockLevels : "userId"
User ||--o{ UserReviews : "userId"

SpikeVaults ||--o{ SpikeVaultLevels : "spikeVaultId"
BoxobanLevels ||--o| BoxobanUserData : "currentLevelId"

' Notes and Constraints
note right of User
**Primary Entity**
- Auto-incrementing ID
- Supports both anonymous and Google auth
- Anonymous users have random names
- Google users have OAuth data
end note

note right of SpikeVaults
**Unique Constraint**
- (userId, slug) must be unique
- Slug is URL-friendly identifier
- Status: "in_progress" | "completed"
- Soft delete with 'deleted' flag
end note

note right of BoxobanLevels
**Level ID Format**
- "{category}-{fileIndex}-{levelIndex}"
- Categories: "medium" | "hard" | "unfiltered"
- Status: "available" | "assigned" | "solved"
- Global synchronized puzzle system
end note

note right of UserReviews
**Review Constraints**
- One review per user (UNIQUE userId)
- Star rating supports quarter increments
- Admin approval required (approved field)
- Only Google-authenticated users can review
end note

note right of EndlessUserData
**Settings Storage**
- JSONB field for EndlessSettings
- Custom level parameters
- One-to-one with User
- Tracks level progression
end note

note right of OverclockUserData
**Premium Mode**
- Requires payment verification
- currentLevel + 30 = actual difficulty
- One-to-one with User
- Premium access control
end note

note bottom of BoxobanGlobalProgress
**Global Statistics**
- Tracks progress per difficulty category
- totalLevels: Total available in category
- solvedLevels: Completed by any user
- discardedLevels: Levels removed from pool
end note

' Data Types Legend
note as DataTypes
**Data Types:**
- INTEGER: 32-bit signed integer
- TEXT: Variable-length string
- BOOLEAN: True/false value
- TIMESTAMP: Date and time
- UUID: Universally unique identifier
- REAL: Floating-point number
- JSONB: Binary JSON storage
- TEXT[]: Array of text values
end note

' Indexes and Performance
note as Indexes
**Key Indexes:**
- userTable.googleId (UNIQUE)
- session.userId + expiresAt
- endless_levels.userId + levelNumber
- spike_vaults.userId + slug (UNIQUE)
- boxoban_levels.assignedTo + status
- user_reviews.userId (UNIQUE)
end note

@enduml

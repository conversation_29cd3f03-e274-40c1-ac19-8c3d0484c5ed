graph TB
    %% Actors
    Guest[Guest User]
    Player[Authenticated Player]
    Admin[Administrator]
    
    %% Authentication System
    subgraph "Authentication System"
        Auth1[Sign In Anonymously]
        Auth2[Sign In with Google]
        Auth3[Sign Out]
    end
    
    %% Core Game Modes
    subgraph "Game Modes"
        subgraph "Endless Mode"
            Endless1[Configure Difficulty Settings]
            Endless2[Play Procedural Levels]
            Endless3[View Progress Records]
            Endless4[Continue Game Session]
        end
        
        subgraph "Spike Vaults"
            Vault1[Create Custom Vault]
            Vault2[Edit Vault Settings]
            Vault3[Delete Vault]
            Vault4[Play Vault Levels]
            Vault5[View Vault Progress]
            Vault6[Share Vault]
        end
        
        subgraph "Boxoban Challenge"
            Boxoban1[Select Difficulty Mode]
            Boxoban2[Play Global Challenges]
            Boxoban3[View Global Progress]
            Boxoban4[Complete Synchronized Levels]
        end
        
        subgraph "Overclock Mode"
            Overclock1[Verify Premium Access]
            Overclock2[Play High-Difficulty Levels]
            Overclock3[View Overclock Records]
            Overclock4[Continue Premium Session]
        end
    end
    
    %% Game Mechanics
    subgraph "Game Mechanics"
        Game1[Move Player Character]
        Game2[Push Boxes to Goals]
        Game3[Reset Current Level]
        Game4[Track Game Statistics]
        Game5[Submit Level Completion]
        Game6[View Game Controls]
    end
    
    %% Content Management
    subgraph "Content Management"
        Content1[Generate Procedural Levels]
        Content2[Validate Solutions]
        Content3[Save Game Progress]
        Content4[Load Game State]
    end
    
    %% User Reviews System
    subgraph "User Reviews"
        Review1[Submit Game Review]
        Review2[Rate Game Experience]
        Review3[View Public Reviews]
    end
    
    %% Admin Features
    subgraph "Admin Panel"
        Admin1[View User Analytics]
        Admin2[Monitor Game Statistics]
        Admin3[Manage User Reviews]
        Admin4[Approve/Reject Reviews]
        Admin5[View System Dashboard]
        Admin6[Track User Engagement]
    end
    
    %% User Interactions
    Guest --> Auth1
    Guest --> Auth2
    Guest --> Review3
    
    Player --> Auth3
    Player --> Endless1
    Player --> Endless2
    Player --> Endless3
    Player --> Endless4
    Player --> Vault1
    Player --> Vault2
    Player --> Vault3
    Player --> Vault4
    Player --> Vault5
    Player --> Vault6
    Player --> Boxoban1
    Player --> Boxoban2
    Player --> Boxoban3
    Player --> Boxoban4
    Player --> Overclock1
    Player --> Overclock2
    Player --> Overclock3
    Player --> Overclock4
    Player --> Game1
    Player --> Game2
    Player --> Game3
    Player --> Game4
    Player --> Game5
    Player --> Game6
    Player --> Review1
    Player --> Review2
    Player --> Review3
    
    Admin --> Admin1
    Admin --> Admin2
    Admin --> Admin3
    Admin --> Admin4
    Admin --> Admin5
    Admin --> Admin6
    Admin --> Review3
    
    %% System Dependencies
    Endless2 --> Content1
    Vault4 --> Content1
    Boxoban2 --> Content4
    Overclock2 --> Content1
    
    Game5 --> Content2
    Game5 --> Content3
    Endless4 --> Content4
    Vault5 --> Content4
    Overclock4 --> Content4
    
    Review1 --> Admin3
    Review2 --> Admin3
    
    %% Styling
    classDef actor fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gameMode fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef system fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef admin fill:#fff3e0,stroke:#e65100,stroke-width:2px
    
    class Guest,Player,Admin actor
    class Endless1,Endless2,Endless3,Endless4,Vault1,Vault2,Vault3,Vault4,Vault5,Vault6,Boxoban1,Boxoban2,Boxoban3,Boxoban4,Overclock1,Overclock2,Overclock3,Overclock4 gameMode
    class Auth1,Auth2,Auth3,Game1,Game2,Game3,Game4,Game5,Game6,Content1,Content2,Content3,Content4,Review1,Review2,Review3 system
    class Admin1,Admin2,Admin3,Admin4,Admin5,Admin6 admin

@startuml Sokoverse Collaboration Diagram

!theme plain
skinparam object {
    BackgroundColor #F8F9FA
    BorderColor #495057
    FontSize 10
}
skinparam note {
    BackgroundColor #FFF3CD
    BorderColor #856404
    FontSize 9
}

title Sokoverse - Collaboration Diagram: Level Completion Flow

' Objects
object ":EndlessGame" as EndlessGame
object ":SokobanCanvasGameBoard" as GameBoard
object ":useKeyboardControls" as KeyboardHook
object ":GameLogic" as GameLogic
object ":useGameCompletion" as CompletionHook
object ":HMACService" as HMACService
object ":EndlessActions" as EndlessActions
object ":SecurityService" as SecurityService
object ":SolutionChecker" as SolutionChecker
object ":DrizzleORM" as Database
object ":LevelCompletionDialog" as CompletionDialog
object ":useToast" as ToastHook

' Scenario 1: Player Makes a Move
note top of EndlessGame : **Scenario 1: Player Makes a Move**

EndlessGame -> GameBoard : 1: render()
GameBoard -> KeyboardHook : 2: useKeyboardControls()
KeyboardHook -> GameLogic : 3: movePlayer(state, direction)
GameLogic -> EndlessGame : 4: return newGameState
EndlessGame -> GameBoard : 5: setGameState(newState)
GameBoard -> CompletionHook : 6: useGameCompletion()

' Scenario 2: Level Completion Detection
note top of CompletionHook : **Scenario 2: Level Completion Detection**

CompletionHook -> EndlessGame : 7: onLevelComplete()
EndlessGame -> HMACService : 8: hmacSign(payload)
HMACService -> EndlessGame : 9: return hash
EndlessGame -> EndlessActions : 10: submitLevel(stats, moves, hash)

' Scenario 3: Server-Side Validation
note top of EndlessActions : **Scenario 3: Server-Side Validation**

EndlessActions -> SecurityService : 11: verifyHash(payload, hash)
SecurityService -> EndlessActions : 12: return isValid
EndlessActions -> SolutionChecker : 13: checkSolution(level, moves)
SolutionChecker -> EndlessActions : 14: return isValidSolution
EndlessActions -> Database : 15: update(endlessLevels)
Database -> EndlessActions : 16: return success

' Scenario 4: UI Feedback
note top of EndlessActions : **Scenario 4: UI Feedback**

EndlessActions -> EndlessGame : 17: return result
EndlessGame -> CompletionDialog : 18: setShowDialog(true)
EndlessGame -> ToastHook : 19: toast.success()
CompletionDialog -> EndlessGame : 20: onNextLevel()
EndlessGame -> EndlessActions : 21: generateEndlessLevel()

' Additional collaboration for Spike Vault creation
note bottom : **Additional Scenario: Spike Vault Creation**

object ":CreateSpikeVaultDialog" as CreateDialog
object ":SpikeVaultActions" as SpikeVaultActions
object ":ValidationSchemas" as ValidationSchemas
object ":LevelGenerator" as LevelGenerator
object ":AutoSokoban" as AutoSokoban

CreateDialog -> ValidationSchemas : 22: validate(formData)
ValidationSchemas -> CreateDialog : 23: return validatedData
CreateDialog -> SpikeVaultActions : 24: createSpikeVault(data)
SpikeVaultActions -> LevelGenerator : 25: generateVaultData()
LevelGenerator -> AutoSokoban : 26: generateSpikeVaultLevel(seed, difficulty)
AutoSokoban -> LevelGenerator : 27: return levelData
LevelGenerator -> SpikeVaultActions : 28: return vaultData
SpikeVaultActions -> Database : 29: insert(spikeVaults)
Database -> SpikeVaultActions : 30: return newVault
SpikeVaultActions -> CreateDialog : 31: return success
CreateDialog -> ToastHook : 32: toast.success("Vault created")

' Authentication flow
note right : **Authentication Scenario**

object ":AuthProvider" as AuthProvider
object ":SessionManager" as SessionManager
object ":GoogleOAuth" as GoogleOAuth
object ":AnonymousAuth" as AnonymousAuth

AuthProvider -> SessionManager : 33: getCurrentSession()
SessionManager -> Database : 34: select(sessions)
Database -> SessionManager : 35: return sessionData
SessionManager -> AuthProvider : 36: return validatedSession

' Anonymous sign-in alternative
AuthProvider -> AnonymousAuth : 37: createAnonymousUser()
AnonymousAuth -> Database : 38: insert(userTable)
Database -> AnonymousAuth : 39: return newUser
AnonymousAuth -> SessionManager : 40: createSession(token, userId)
SessionManager -> Database : 41: insert(sessionTable)
Database -> SessionManager : 42: return session
SessionManager -> AnonymousAuth : 43: return sessionData
AnonymousAuth -> AuthProvider : 44: return {user, session}

' Google OAuth alternative
AuthProvider -> GoogleOAuth : 45: handleCallback()
GoogleOAuth -> Database : 46: upsert(userTable)
Database -> GoogleOAuth : 47: return user
GoogleOAuth -> SessionManager : 48: createSession(token, userId)
SessionManager -> GoogleOAuth : 49: return session
GoogleOAuth -> AuthProvider : 50: return {user, session}

' Message sequence styling
note as N1
**Message Flow Legend:**
1-6: User Input & Game State Update
7-10: Level Completion Trigger
11-16: Server Validation & Persistence
17-21: UI Feedback & Next Level
22-32: Vault Creation Flow
33-50: Authentication Flows
end note

@enduml
